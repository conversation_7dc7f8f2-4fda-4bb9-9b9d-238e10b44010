using UnityEngine;
using UnityEngine.UI;

namespace HELLSTRIKE
{
    public class SkullInventoryUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Image skullImage; // The UI Image that shows the skull
        [SerializeField] private bool autoFindSkullImage = true;
        [SerializeField] private string skullImageName = "SkullImage"; // Name to search for if auto-finding
        
        [Head<PERSON>("Skull Sprites")]
        [SerializeField] private Sprite blueSkullSprite;
        [SerializeField] private Sprite redSkullSprite;
        [SerializeField] private Sprite emptySprite; // Optional: sprite to show when no skull
        
        [Header("Animation Settings")]
        [SerializeField] private bool enableAnimation = true;
        [SerializeField] private float animationDuration = 0.3f;
        [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private bool scaleAnimation = true;
        [SerializeField] private Vector3 animationScale = Vector3.one * 1.2f;
        
        [Header("Positioning (Only used if Auto Position is enabled)")]
        [SerializeField] private bool positionOnLeft = true; // Position on left side of screen
        [SerializeField] private Vector2 screenOffset = new Vector2(100f, 100f); // Offset from screen edge
        [SerializeField] private bool autoPosition = false; // DISABLED - Position the UI manually in the scene
        
        [Header("Debug")]
        [SerializeField] private bool debugUI = true;
        
        // Private variables
        private bool isVisible = false;
        private SkullType currentSkullType = SkullType.Blue;
        private Vector3 originalScale;
        private RectTransform rectTransform;
        private CanvasGroup canvasGroup;
        private float animationTimer = 0f;
        private bool isAnimating = false;
        
        void Start()
        {
            // Auto-find skull image if enabled
            if (autoFindSkullImage && skullImage == null)
            {
                // Try to find by name first
                GameObject skullImageObj = GameObject.Find(skullImageName);
                if (skullImageObj != null)
                {
                    skullImage = skullImageObj.GetComponent<Image>();
                }
                
                // If not found, try to find as child
                if (skullImage == null)
                {
                    Transform found = transform.Find(skullImageName);
                    if (found != null)
                    {
                        skullImage = found.GetComponent<Image>();
                    }
                }
                
                // If still not found, try to get from this GameObject
                if (skullImage == null)
                {
                    skullImage = GetComponent<Image>();
                }
            }
            
            if (skullImage == null)
            {
                Debug.LogError("SkullInventoryUI: No skull image found! Please assign one in the inspector or ensure auto-find settings are correct.");
                return;
            }
            
            // Get components
            rectTransform = skullImage.GetComponent<RectTransform>();
            canvasGroup = skullImage.GetComponent<CanvasGroup>();
            
            // Add CanvasGroup if it doesn't exist
            if (canvasGroup == null)
            {
                canvasGroup = skullImage.gameObject.AddComponent<CanvasGroup>();
            }
            
            // Store original scale
            originalScale = rectTransform.localScale;
            
            // Auto-position if enabled (DISABLED BY DEFAULT - user can position manually)
            if (autoPosition)
            {
                PositionOnScreen();
            }
            else if (debugUI)
            {
                Debug.Log("SkullInventoryUI: Auto-positioning disabled. Using manual positioning.");
            }
            
            // Initialize as hidden
            HideSkull();
            
            if (debugUI)
            {
                Debug.Log($"SkullInventoryUI: Initialized. Skull image found: {skullImage != null}");
            }
        }
        
        void Update()
        {
            // Handle animation
            if (isAnimating && enableAnimation)
            {
                UpdateAnimation();
            }
        }
        
        private void PositionOnScreen()
        {
            if (rectTransform == null) return;
            
            // Get canvas for screen size reference
            Canvas canvas = GetComponentInParent<Canvas>();
            if (canvas == null) return;
            
            RectTransform canvasRect = canvas.GetComponent<RectTransform>();
            if (canvasRect == null) return;
            
            // Calculate position based on screen side
            Vector2 anchorMin, anchorMax, anchoredPosition;
            
            if (positionOnLeft)
            {
                // Position on left side
                anchorMin = new Vector2(0f, 1f); // Top-left
                anchorMax = new Vector2(0f, 1f);
                anchoredPosition = new Vector2(screenOffset.x, -screenOffset.y);
            }
            else
            {
                // Position on right side
                anchorMin = new Vector2(1f, 1f); // Top-right
                anchorMax = new Vector2(1f, 1f);
                anchoredPosition = new Vector2(-screenOffset.x, -screenOffset.y);
            }
            
            rectTransform.anchorMin = anchorMin;
            rectTransform.anchorMax = anchorMax;
            rectTransform.anchoredPosition = anchoredPosition;
            
            if (debugUI)
            {
                Debug.Log($"SkullInventoryUI: Positioned on {(positionOnLeft ? "left" : "right")} side of screen");
            }
        }
        
        public void ShowSkull(SkullType skullType)
        {
            if (skullImage == null) return;
            
            currentSkullType = skullType;
            isVisible = true;
            
            // Set appropriate sprite
            Sprite spriteToShow = skullType == SkullType.Blue ? blueSkullSprite : redSkullSprite;
            if (spriteToShow != null)
            {
                skullImage.sprite = spriteToShow;
            }
            
            // Show the image
            skullImage.gameObject.SetActive(true);
            
            // Set alpha
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 1f;
            }
            
            // Trigger animation
            if (enableAnimation)
            {
                StartShowAnimation();
            }
            
            if (debugUI)
            {
                Debug.Log($"SkullInventoryUI: Showing {skullType} skull");
            }
        }
        
        public void HideSkull()
        {
            if (skullImage == null) return;
            
            isVisible = false;
            
            // Hide the image
            if (enableAnimation)
            {
                StartHideAnimation();
            }
            else
            {
                skullImage.gameObject.SetActive(false);
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = 0f;
                }
            }
            
            // Set empty sprite if available
            if (emptySprite != null)
            {
                skullImage.sprite = emptySprite;
            }
            
            if (debugUI)
            {
                Debug.Log("SkullInventoryUI: Hiding skull");
            }
        }
        
        private void StartShowAnimation()
        {
            if (!enableAnimation) return;
            
            isAnimating = true;
            animationTimer = 0f;
            
            // Reset scale
            if (rectTransform != null)
            {
                rectTransform.localScale = originalScale;
            }
        }
        
        private void StartHideAnimation()
        {
            if (!enableAnimation) return;
            
            isAnimating = true;
            animationTimer = 0f;
        }
        
        private void UpdateAnimation()
        {
            animationTimer += Time.deltaTime;
            float progress = Mathf.Clamp01(animationTimer / animationDuration);
            float curveValue = animationCurve.Evaluate(progress);
            
            if (isVisible)
            {
                // Show animation
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = curveValue;
                }
                
                if (scaleAnimation && rectTransform != null)
                {
                    Vector3 targetScale = Vector3.Lerp(originalScale * 0.5f, originalScale, curveValue);
                    rectTransform.localScale = targetScale;
                }
            }
            else
            {
                // Hide animation
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = 1f - curveValue;
                }
                
                if (scaleAnimation && rectTransform != null)
                {
                    Vector3 targetScale = Vector3.Lerp(originalScale, originalScale * 0.5f, curveValue);
                    rectTransform.localScale = targetScale;
                }
            }
            
            // End animation
            if (progress >= 1f)
            {
                isAnimating = false;
                
                if (!isVisible)
                {
                    skullImage.gameObject.SetActive(false);
                }
                
                // Reset scale
                if (rectTransform != null)
                {
                    rectTransform.localScale = originalScale;
                }
            }
        }
        
        // Public methods for external control
        public void SetSkullSprites(Sprite blueSprite, Sprite redSprite, Sprite emptySprite = null)
        {
            blueSkullSprite = blueSprite;
            redSkullSprite = redSprite;
            this.emptySprite = emptySprite;
        }
        
        public void SetSkullImage(Image newImage)
        {
            skullImage = newImage;
            if (skullImage != null)
            {
                rectTransform = skullImage.GetComponent<RectTransform>();
                canvasGroup = skullImage.GetComponent<CanvasGroup>();
                
                if (canvasGroup == null)
                {
                    canvasGroup = skullImage.gameObject.AddComponent<CanvasGroup>();
                }
                
                originalScale = rectTransform.localScale;
            }
        }
        
        public void ForceRefresh()
        {
            if (isVisible)
            {
                ShowSkull(currentSkullType);
            }
            else
            {
                HideSkull();
            }
        }
        
        // Properties
        public bool IsVisible => isVisible;
        public SkullType CurrentSkullType => currentSkullType;
        public Image SkullImage => skullImage;
    }
}

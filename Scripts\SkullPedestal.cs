using UnityEngine;
using UnityEngine.Events;

namespace HELLSTRIKE
{
    public class SkullPedestal : MonoBehaviour
    {
        [Header("Pedestal Settings")]
        [SerializeField] private SkullType requiredSkullType = SkullType.Blue;
        [SerializeField] private float interactionRange = 3f;
        [SerializeField] private LayerMask playerLayer = -1;
        [SerializeField] private bool debugPedestal = true;
        
        [Header("Visual Settings")]
        [SerializeField] private Transform skullPlacementPoint; // Where the skull appears when placed
        [SerializeField] private GameObject skullVisualPrefab; // Visual representation of the skull
        [SerializeField] private bool enableGlow = true;
        [SerializeField] private Color emptyGlowColor = Color.red;
        [SerializeField] private Color filledGlowColor = Color.green;
        [SerializeField] private float glowIntensity = 1f;
        
        [Header("Audio")]
        [SerializeField] private AudioClip placeSound;
        [SerializeField] private AudioClip removeSound;
        [Range(0f, 1f)] [SerializeField] private float audioVolume = 0.7f;
        
        [Header("Effects")]
        [SerializeField] private GameObject placeEffect;
        [SerializeField] private GameObject removeEffect;
        [SerializeField] private float effectLifetime = 2f;
        
        [Header("Events")]
        public UnityEvent OnSkullPlaced;
        public UnityEvent OnSkullRemoved;
        
        // Private variables
        private bool hasSkull = false;
        private GameObject currentSkullVisual;
        private SkullPuzzleManager puzzleManager;
        private AudioSource audioSource;
        private Renderer pedestalRenderer;
        private Material originalMaterial;
        private bool playerInRange = false;
        
        void Start()
        {
            // Find puzzle manager
            puzzleManager = FindFirstObjectByType<SkullPuzzleManager>();
            if (puzzleManager == null && debugPedestal)
            {
                Debug.LogWarning("SkullPedestal: SkullPuzzleManager not found in scene!");
            }

            // Setup audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Get renderer for glow effects
            pedestalRenderer = GetComponent<Renderer>();
            if (pedestalRenderer != null)
            {
                originalMaterial = pedestalRenderer.material;
            }

            // Auto-find skull placement point if not set
            if (skullPlacementPoint == null)
            {
                skullPlacementPoint = transform;
                if (debugPedestal)
                {
                    Debug.Log($"SkullPedestal: Using pedestal transform as placement point");
                }
            }

            // Initialize visual state
            UpdateVisualState();

            if (debugPedestal)
            {
                Debug.Log($"SkullPedestal: Initialized for {requiredSkullType} skull at {transform.position}");
                Debug.Log($"SkullPedestal: Skull Visual Prefab assigned: {skullVisualPrefab != null}");
                Debug.Log($"SkullPedestal: Placement Point: {skullPlacementPoint.name} at {skullPlacementPoint.position}");
            }
        }
        
        void Update()
        {
            CheckForPlayerInteraction();
        }
        
        private void CheckForPlayerInteraction()
        {
            // Find player if not found
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            if (playerObj == null) return;
            
            float distanceToPlayer = Vector3.Distance(transform.position, playerObj.transform.position);
            bool wasInRange = playerInRange;
            playerInRange = distanceToPlayer <= interactionRange;
            
            // Handle interaction input when player is in range
            if (playerInRange && Input.GetKeyDown(KeyCode.E))
            {
                if (debugPedestal)
                {
                    Debug.Log($"SkullPedestal: Player pressed E near {requiredSkullType} pedestal");
                }
                HandleInteraction();
            }
            
            // Debug info when player enters/exits range
            if (playerInRange != wasInRange && debugPedestal)
            {
                if (playerInRange)
                {
                    string action = hasSkull ? "remove skull" : 
                                   (puzzleManager != null && puzzleManager.PlayerHasSkull() && puzzleManager.GetPlayerSkullType() == requiredSkullType) ? "place skull" : "no valid action";
                    Debug.Log($"SkullPedestal: Player in range. Press E to {action}");
                }
            }
        }
        
        private void HandleInteraction()
        {
            if (puzzleManager == null)
            {
                Debug.LogWarning("SkullPedestal: Cannot interact - no SkullPuzzleManager found!");
                return;
            }

            if (debugPedestal)
            {
                Debug.Log($"SkullPedestal: Handling interaction. HasSkull: {hasSkull}, PlayerHasSkull: {puzzleManager.PlayerHasSkull()}");
                if (puzzleManager.PlayerHasSkull())
                {
                    Debug.Log($"SkullPedestal: Player has {puzzleManager.GetPlayerSkullType()} skull, pedestal requires {requiredSkullType}");
                }
            }

            if (hasSkull)
            {
                // Remove skull from pedestal
                if (debugPedestal)
                {
                    Debug.Log($"SkullPedestal: Removing {requiredSkullType} skull from pedestal");
                }
                RemoveSkull();
            }
            else if (puzzleManager.PlayerHasSkull())
            {
                // Check if player has the correct skull type
                if (puzzleManager.GetPlayerSkullType() == requiredSkullType)
                {
                    if (debugPedestal)
                    {
                        Debug.Log($"SkullPedestal: Placing {requiredSkullType} skull on pedestal");
                    }
                    PlaceSkull();
                }
                else
                {
                    if (debugPedestal)
                    {
                        Debug.Log($"SkullPedestal: Wrong skull type! This pedestal requires {requiredSkullType} skull, but player has {puzzleManager.GetPlayerSkullType()}.");
                    }
                }
            }
            else
            {
                if (debugPedestal)
                {
                    Debug.Log("SkullPedestal: Player doesn't have a skull to place!");
                }
            }
        }
        
        private void PlaceSkull()
        {
            if (hasSkull || puzzleManager == null) return;

            // Take skull from player
            puzzleManager.PlaceSkull();
            hasSkull = true;

            // Create visual representation
            if (skullVisualPrefab != null && skullPlacementPoint != null)
            {
                currentSkullVisual = Instantiate(skullVisualPrefab, skullPlacementPoint.position, skullPlacementPoint.rotation, skullPlacementPoint);

                if (debugPedestal)
                {
                    Debug.Log($"SkullPedestal: Created skull visual from prefab at {skullPlacementPoint.position}");
                }
            }
            else if (skullPlacementPoint != null)
            {
                // Fallback: Create a simple cube as skull visual
                currentSkullVisual = GameObject.CreatePrimitive(PrimitiveType.Cube);
                currentSkullVisual.transform.position = skullPlacementPoint.position;
                currentSkullVisual.transform.rotation = skullPlacementPoint.rotation;
                currentSkullVisual.transform.parent = skullPlacementPoint;
                currentSkullVisual.transform.localScale = Vector3.one * 0.3f; // Make it smaller
                currentSkullVisual.name = $"{requiredSkullType}Skull_Visual";

                // Color it based on skull type
                Renderer skullRenderer = currentSkullVisual.GetComponent<Renderer>();
                if (skullRenderer != null)
                {
                    Material skullMat = new Material(Shader.Find("Standard"));
                    skullMat.color = requiredSkullType == SkullType.Blue ? Color.blue : Color.red;
                    skullRenderer.material = skullMat;
                }

                if (debugPedestal)
                {
                    Debug.Log($"SkullPedestal: Created fallback skull visual (cube) at {skullPlacementPoint.position}");
                }
            }
            else
            {
                if (debugPedestal)
                {
                    Debug.LogError($"SkullPedestal: Cannot create skull visual! SkullVisualPrefab: {skullVisualPrefab != null}, PlacementPoint: {skullPlacementPoint != null}");
                }
            }

            // Play effects
            PlayPlaceEffects();

            // Update visual state
            UpdateVisualState();

            // Notify puzzle manager
            if (puzzleManager != null)
            {
                puzzleManager.OnSkullPlacedOnPedestal(requiredSkullType);
            }

            // Trigger events
            OnSkullPlaced?.Invoke();

            if (debugPedestal)
            {
                Debug.Log($"SkullPedestal: {requiredSkullType} skull placed successfully!");
            }
        }
        
        private void RemoveSkull()
        {
            if (!hasSkull || puzzleManager == null) return;
            
            // Give skull back to player
            puzzleManager.PickupSkull(requiredSkullType);
            hasSkull = false;
            
            // Remove visual representation
            if (currentSkullVisual != null)
            {
                Destroy(currentSkullVisual);
                currentSkullVisual = null;
            }
            
            // Play effects
            PlayRemoveEffects();
            
            // Update visual state
            UpdateVisualState();
            
            // Notify puzzle manager
            if (puzzleManager != null)
            {
                puzzleManager.OnSkullRemovedFromPedestal(requiredSkullType);
            }
            
            // Trigger events
            OnSkullRemoved?.Invoke();
            
            if (debugPedestal)
            {
                Debug.Log($"SkullPedestal: {requiredSkullType} skull removed!");
            }
        }
        
        private void PlayPlaceEffects()
        {
            // Play sound
            if (placeSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(placeSound, audioVolume);
            }
            
            // Spawn effect
            if (placeEffect != null)
            {
                GameObject effect = Instantiate(placeEffect, transform.position, transform.rotation);
                Destroy(effect, effectLifetime);
            }
        }
        
        private void PlayRemoveEffects()
        {
            // Play sound
            if (removeSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(removeSound, audioVolume);
            }
            
            // Spawn effect
            if (removeEffect != null)
            {
                GameObject effect = Instantiate(removeEffect, transform.position, transform.rotation);
                Destroy(effect, effectLifetime);
            }
        }
        
        private void UpdateVisualState()
        {
            if (!enableGlow || pedestalRenderer == null) return;
            
            // Change glow color based on state
            Color targetColor = hasSkull ? filledGlowColor : emptyGlowColor;
            
            // Apply glow effect (this is a simple example - you might want to use a proper glow shader)
            if (pedestalRenderer.material != null)
            {
                if (pedestalRenderer.material.HasProperty("_EmissionColor"))
                {
                    pedestalRenderer.material.SetColor("_EmissionColor", targetColor * glowIntensity);
                }
                else if (pedestalRenderer.material.HasProperty("_Color"))
                {
                    pedestalRenderer.material.SetColor("_Color", targetColor);
                }
            }
        }
        
        // Visual feedback in editor
        void OnDrawGizmosSelected()
        {
            // Draw interaction range
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, interactionRange);
            
            // Draw skull placement point
            if (skullPlacementPoint != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(skullPlacementPoint.position, Vector3.one * 0.3f);
            }
        }
        
        // Public methods
        public bool HasSkull => hasSkull;
        public SkullType RequiredSkullType => requiredSkullType;
        public float InteractionRange => interactionRange;
        
        public void SetPuzzleManager(SkullPuzzleManager manager)
        {
            puzzleManager = manager;
        }
        
        public void ForceRemoveSkull()
        {
            if (hasSkull)
            {
                hasSkull = false;
                if (currentSkullVisual != null)
                {
                    Destroy(currentSkullVisual);
                    currentSkullVisual = null;
                }
                UpdateVisualState();
            }
        }
    }
}
